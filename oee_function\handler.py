from datetime import datetime
from typing import Optional

from cognite.client import CogniteClient

from app.infra.env_variables import EnvVariables
from app.infra.logger_adapter import get_logger
from app.infra.services_factory import create_event_frame_service
from app.utils.constants import EventFrameConstants

log = get_logger()


def handle(
    client: CogniteClient = None,
    data: dict = None,
    secrets: dict = None,
    function_call_info: dict = None,
) -> None:
    if data["site_external_id"] in EventFrameConstants.IGNORE_SITES:
        log.info(
            "Selected Site is currently set for manual OEE inserts only: "
            + data["site_external_id"]
        )
        exit()

    export_xls: bool = data.get("export_xls", False)
    save_events: bool = data.get("save_events", True)

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    if "start_time" in data:
        start_time = datetime.fromisoformat(data["start_time"])

    if "end_time" in data:
        end_time = datetime.fromisoformat(data["end_time"])

    log.info("Starting OEE event frames")

    variables = EnvVariables()
    log.info(f"Cognite configs: {variables.cognite.model_dump()}")
    service = create_event_frame_service(variables)
    service.transform_time_series_to_event_frames(
        start_time=start_time,
        end_time=end_time,
        reporting_site_external_id=str(data["site_external_id"]),
        only_lines=data.get("only_lines", None),
        export_xls=export_xls,
        save_events=save_events,
    )

    log.info("PI tags successfully transformed into OEE event frames")


if __name__ == "__main__":
    # handle(data={
    #     "only_lines": ["RLN-NANCELLN1"],
    #     "site_external_id": "STS-NAN",
    #     "export_xls": True,
    #     "save_events": False,
    # })
    # handle(data={"only_lines": ["RLN-WASSCDEXA"], "site_external_id": "STS-WAS"})
    handle({
        "only_lines": [
            "RLN-GELVAE6K2",
            "RLN-GELVAE6K3",
            "RLN-GELVAE6K4"
        ],
        "site_external_id": "STS-GEL"
    })
